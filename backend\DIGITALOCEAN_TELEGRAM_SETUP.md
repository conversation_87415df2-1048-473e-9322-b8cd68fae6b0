# إعداد التلغرام في DigitalOcean App Platform

## 🎯 المشكلة
عند النقر على زر "إرسال للدعم" تظهر رسالة خطأ ولا تصل الرسالة إلى التلغرام.

## 🔍 السبب
متغيرات البيئة للتلغرام غير مُعرفة في DigitalOcean App Platform.

## ✅ الحل الكامل

### 1. إنشاء بوت تلغرام (إذا لم يكن موجوداً)

1. **افتح التلغرام وابحث عن:** `@BotFather`
2. **أرسل:** `/newbot`
3. **اتبع التعليمات:**
   - اختر اسم للبوت (مثل: Montajati Support Bot)
   - اختر username للبوت (مثل: montajati_support_bot)
4. **احفظ الـ BOT_TOKEN** الذي ستحصل عليه (مثل: `123456789:ABCdefGHIjklMNOpqrsTUVwxyz`)

### 2. الحصول على Chat ID

**الطريقة الأولى (الأسهل):**
1. ابحث عن البوت الذي أنشأته في التلغرام
2. أرسل له `/start`
3. أرسل أي رسالة (مثل: "مرحبا")
4. افتح الرابط التالي في المتصفح:
   ```
   https://api.telegram.org/bot<BOT_TOKEN>/getUpdates
   ```
   (استبدل `<BOT_TOKEN>` بالرمز الحقيقي)
5. ابحث عن `"chat":{"id":XXXXXXX}` واحفظ الرقم

**مثال:**
```json
{
  "message": {
    "chat": {
      "id": 123456789,
      "first_name": "أحمد",
      "type": "private"
    }
  }
}
```
في هذا المثال، الـ Chat ID هو: `123456789`

### 3. إضافة المتغيرات في DigitalOcean

1. **اذهب إلى:** [DigitalOcean App Platform](https://cloud.digitalocean.com/apps)
2. **اختر تطبيقك:** `montajati-backend`
3. **اذهب إلى:** `Settings` > `Environment Variables`
4. **أضف المتغيرات التالية:**

```
TELEGRAM_BOT_TOKEN = 123456789:ABCdefGHIjklMNOpqrsTUVwxyz
TELEGRAM_CHAT_ID = 123456789
```

**ملاحظة:** تأكد من تحديد `Encrypted` لكلا المتغيرين لحماية البيانات.

### 4. إعادة نشر التطبيق

بعد إضافة المتغيرات:
1. اضغط `Save`
2. سيتم إعادة نشر التطبيق تلقائياً
3. انتظر حتى يكتمل النشر (عادة 2-5 دقائق)

## 🧪 اختبار النظام

### اختبار من الكمبيوتر:
```bash
cd backend
node test_digitalocean_telegram.js
```

### اختبار من التطبيق:
1. افتح تطبيق Flutter
2. اذهب إلى صفحة الطلبات
3. اختر أي طلب
4. اضغط "إرسال للدعم"
5. أضف ملاحظة واضغط "إرسال"
6. تحقق من وصول الرسالة في التلغرام

## 🔧 استكشاف الأخطاء

### خطأ "Bot token not configured"
**السبب:** `TELEGRAM_BOT_TOKEN` غير موجود في DigitalOcean
**الحل:** أضف المتغير في Environment Variables

### خطأ "chat not found"
**السبب:** `TELEGRAM_CHAT_ID` غير صحيح أو المستخدم لم يرسل /start للبوت
**الحل:** 
1. أرسل `/start` للبوت
2. تأكد من صحة Chat ID

### خطأ "Unauthorized"
**السبب:** `TELEGRAM_BOT_TOKEN` غير صحيح
**الحل:** تأكد من نسخ الرمز بشكل صحيح من BotFather

### خطأ "bot was blocked"
**السبب:** تم حظر البوت من قبل المستخدم
**الحل:** ألغِ حظر البوت وأرسل `/start`

## 📱 مثال على رسالة الدعم

عند إرسال طلب دعم، ستصل رسالة مثل هذه:

```
👤 معلومات الزبون:
📝 الاسم: أحمد محمد علي
📞 الهاتف الأساسي: 07901234567
📱 الهاتف البديل: 07801234567

📍 معلومات العنوان:
🏛️ المحافظة: بغداد
🏠 العنوان: حي الكرادة - شارع الرشيد

📦 معلومات الطلب:
🆔 رقم الطلب: order_1234567890
📅 تاريخ الطلب: 29/07/2025
⚠️ حالة الطلب: لا يرد
🚚 رقم الطلب في التوصيل: WAS_123456

💬 ملاحظات المستخدم:
العميل لا يرد على الهاتف منذ يومين
```

## 🎯 النتيجة المتوقعة

بعد تطبيق هذه الخطوات:
- ✅ ستصل رسائل الدعم إلى التلغرام فوراً
- ✅ ستظهر رسالة "تم إرسال طلب الدعم بنجاح" في التطبيق
- ✅ سيتم تحديث حالة الطلب في قاعدة البيانات
- ✅ ستحصل على إشعار فوري في التلغرام لكل طلب دعم

## 📞 الدعم
إذا واجهت أي مشكلة، شغل ملف الاختبار وأرسل النتائج للمطور.
