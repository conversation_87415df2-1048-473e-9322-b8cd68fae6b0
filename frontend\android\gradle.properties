org.gradle.jvmargs=-Xmx6G -Dfile.encoding=UTF-8 -XX:+UseG1GC
android.useAndroidX=true
android.enableJetifier=true

# Kotlin daemon settings - تعطيل daemon لتجنب مشاكل الاتصال
kotlin.daemon.jvmargs=-Xmx3G -Dfile.encoding=UTF-8
kotlin.incremental=false
kotlin.caching.enabled=false
kotlin.compiler.execution.strategy=in-process

# Gradle daemon settings - تعطيل كامل لتجنب التضارب
org.gradle.daemon=false
org.gradle.parallel=false
org.gradle.configureondemand=false
org.gradle.workers.max=1
org.gradle.unsafe.configuration-cache=false
org.gradle.unsafe.configuration-cache-problems=warn

# Android build settings
android.enableR8.fullMode=false
android.useAndroidX=true
android.enableJetifier=true
