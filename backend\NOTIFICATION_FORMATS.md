# 📱 تنسيق الإشعارات الجديد

## 🎯 القواعد الأساسية:

### العنوان:
- **حالة الطلب + إيموجي مناسب**
- مثال: `🚗 قيد التوصيل`

### الرسالة:
- **اسم العميل - (الحالة الجديدة)**
- مثال: `أحمد محمد - (قيد التوصيل)`

---

## 📋 جميع أنواع الإشعارات:

### 🔵 الحالات الأساسية:

| الحالة | العنوان | الرسالة |
|--------|---------|---------|
| `active` | 📦 نشط | اسم العميل - (نشط) |
| `in_delivery` | 🚗 قيد التوصيل | اسم العميل - (قيد التوصيل) |
| `delivered` | ✅ تم التسليم | اسم العميل - (تم التسليم) |
| `cancelled` | ❌ ملغي | اسم العميل - (ملغي) |

---

### 🔶 حالات الوسيط التفصيلية:

#### 📦 الحالات النشطة:
| الحالة | العنوان | الرسالة |
|--------|---------|---------|
| `فعال` | 📦 فعال | اسم العميل - (فعال) |
| `تم تغيير محافظة الزبون` | 📍 تغيير المحافظة | اسم العميل - (تم تغيير محافظة الزبون) |

#### 📞 حالات عدم الرد:
| الحالة | العنوان | الرسالة |
|--------|---------|---------|
| `لا يرد` | 📞 لا يرد | اسم العميل - (لا يرد) |
| `لا يرد بعد الاتفاق` | 📞 لا يرد بعد الاتفاق | اسم العميل - (لا يرد بعد الاتفاق) |
| `لا يمكن الاتصال بالرقم` | 📞 لا يمكن الاتصال | اسم العميل - (لا يمكن الاتصال بالرقم) |

#### 🔒 حالات الإغلاق:
| الحالة | العنوان | الرسالة |
|--------|---------|---------|
| `مغلق` | 🔒 مغلق | اسم العميل - (مغلق) |
| `مغلق بعد الاتفاق` | 🔒 مغلق بعد الاتفاق | اسم العميل - (مغلق بعد الاتفاق) |

#### ⏰ حالات التأجيل:
| الحالة | العنوان | الرسالة |
|--------|---------|---------|
| `مؤجل` | ⏰ مؤجل | اسم العميل - (مؤجل) |
| `مؤجل لحين اعادة الطلب لاحقا` | ⏰ مؤجل لاحقاً | اسم العميل - (مؤجل لحين اعادة الطلب لاحقا) |

#### 🚗 حالات التوصيل:
| الحالة | العنوان | الرسالة |
|--------|---------|---------|
| `قيد التوصيل الى الزبون (في عهدة المندوب)` | 🚗 قيد التوصيل | اسم العميل - (قيد التوصيل) |

#### ✅ حالات التسليم:
| الحالة | العنوان | الرسالة |
|--------|---------|---------|
| `مستلم مسبقا` | ✅ مستلم مسبقاً | اسم العميل - (مستلم مسبقا) |

#### ❌ حالات الإلغاء:
| الحالة | العنوان | الرسالة |
|--------|---------|---------|
| `الغاء الطلب` | ❌ إلغاء الطلب | اسم العميل - (الغاء الطلب) |
| `رفض الطلب` | 🚫 رفض الطلب | اسم العميل - (رفض الطلب) |
| `مفصول عن الخدمة` | ⛔ مفصول عن الخدمة | اسم العميل - (مفصول عن الخدمة) |
| `طلب مكرر` | 🔄 طلب مكرر | اسم العميل - (طلب مكرر) |

#### 📱 حالات الهاتف:
| الحالة | العنوان | الرسالة |
|--------|---------|---------|
| `الرقم غير معرف` | 📱 رقم غير معرف | اسم العميل - (الرقم غير معرف) |
| `الرقم غير داخل في الخدمة` | 📱 رقم خارج الخدمة | اسم العميل - (الرقم غير داخل في الخدمة) |

#### 📍 حالات العنوان:
| الحالة | العنوان | الرسالة |
|--------|---------|---------|
| `العنوان غير دقيق` | 📍 عنوان غير دقيق | اسم العميل - (العنوان غير دقيق) |

#### 🤷 حالات أخرى:
| الحالة | العنوان | الرسالة |
|--------|---------|---------|
| `لم يطلب` | 🤷 لم يطلب | اسم العميل - (لم يطلب) |
| `حظر المندوب` | 🚫 حظر المندوب | اسم العميل - (حظر المندوب) |
| `تغيير المندوب` | 👤 تغيير المندوب | اسم العميل - (تغيير المندوب) |

---

## 🧪 أمثلة عملية:

### مثال 1: طلب قيد التوصيل
```
العنوان: 🚗 قيد التوصيل
الرسالة: أحمد محمد - (قيد التوصيل)
```

### مثال 2: طلب تم تسليمه
```
العنوان: ✅ تم التسليم
الرسالة: فاطمة علي - (تم التسليم)
```

### مثال 3: طلب لا يرد
```
العنوان: 📞 لا يرد
الرسالة: محمد أحمد - (لا يرد)
```

### مثال 4: طلب ملغي
```
العنوان: ❌ إلغاء الطلب
الرسالة: سارة محمود - (الغاء الطلب)
```

---

## 🔧 كيفية الاختبار:

### اختبار جميع الإشعارات:
```bash
cd backend
node test_all_notifications.js
```

### اختبار إشعار واحد:
```bash
cd backend
node test_all_notifications.js single [رقم_الهاتف] [الحالة] [اسم_العميل]
```

مثال:
```bash
node test_all_notifications.js single 0501234567 "قيد التوصيل" "أحمد محمد"
```

---

## 💰 إشعارات السحب:

### 1. عند تحويل المبلغ (processed/completed):
```
العنوان: 💛💛💛 قلب ذهبي
الرسالة: تم تحويل مبلغ 50000 د.ع الى محفظتك
```

### 2. عند إلغاء عملية السحب (rejected/cancelled):
```
العنوان: 💔💔💔 قلب مكسور
الرسالة: تم الغاء عملية سحبك 50000 د.ع
```

### 3. للحالات الأخرى (pending/approved):
```
العنوان: 💰 تحديث طلب السحب
الرسالة: تم تحديث حالة طلب سحب 50000 د.ع إلى: في انتظار المراجعة
```

---

## ✨ المميزات:

✅ **تنسيق موحد** لجميع الإشعارات  
✅ **إيموجي مناسب** لكل حالة  
✅ **رسائل واضحة** باللغة العربية  
✅ **لا ذكر للوسيط** في الإشعارات  
✅ **تغطية شاملة** لجميع حالات الوسيط  
✅ **سهولة الفهم** للتاجر  

---

## 📝 ملاحظات مهمة:

1. **لا يتم ذكر اسم الوسيط** في أي إشعار
2. **الإيموجي جزء من العنوان** وليس الرسالة
3. **اسم العميل يأتي أولاً** في الرسالة
4. **الحالة بين أقواس** في نهاية الرسالة
5. **تنسيق موحد** لجميع الحالات
