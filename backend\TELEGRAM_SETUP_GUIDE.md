# دليل إعداد التلغرام للدعم

## 🎯 المشكلة الحالية
عند النقر على زر "إرسال للدعم" تظهر رسالة خطأ ولا تصل الرسالة إلى التلغرام.

## 🔍 السبب
متغيرات البيئة للتلغرام غير مُعرفة في Render.

## ✅ الحل

### 1. إنشاء بوت تلغرام (إذا لم يكن موجوداً)
1. افتح التلغرام وابحث عن `@BotFather`
2. أرسل `/newbot`
3. اتبع التعليمات لإنشاء البوت
4. احفظ الـ `BOT_TOKEN` الذي ستحصل عليه

### 2. الحصول على Chat ID
**الطريقة الأولى (الأسهل):**
1. ابحث عن البوت الذي أنشأته
2. أرسل له `/start`
3. أر<PERSON><PERSON> أي رسالة
4. شغ<PERSON> ملف الاختبار: `node test_telegram_support_fix.js`
5. سيظهر لك الـ Chat ID

**الطريقة الثانية:**
1. أرسل رسالة للبوت
2. افتح: `https://api.telegram.org/bot<BOT_TOKEN>/getUpdates`
3. ابحث عن `"chat":{"id":XXXXXXX}`

### 3. إضافة المتغيرات في Render
1. اذهب إلى [Render Dashboard](https://dashboard.render.com)
2. اختر مشروعك `montajati-official-backend`
3. اذهب إلى **Environment**
4. أضف المتغيرات التالية:

```
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHAT_ID=your_chat_id_here
```

### 4. إعادة نشر التطبيق
بعد إضافة المتغيرات، سيتم إعادة نشر التطبيق تلقائياً.

## 🧪 اختبار النظام

### اختبار محلي:
```bash
cd backend
node test_telegram_support_fix.js
```

### اختبار من التطبيق:
1. افتح التطبيق
2. اذهب إلى صفحة الطلبات
3. اختر طلب واضغط "إرسال للدعم"
4. تحقق من وصول الرسالة في التلغرام

## 🔧 استكشاف الأخطاء

### خطأ "Bot token not configured"
- تأكد من إضافة `TELEGRAM_BOT_TOKEN` في Render
- تأكد من صحة الرمز

### خطأ "chat not found"
- تأكد من إرسال `/start` للبوت
- تأكد من صحة `TELEGRAM_CHAT_ID`

### خطأ "Unauthorized"
- تأكد من صحة `TELEGRAM_BOT_TOKEN`
- تأكد من أن البوت لم يتم حذفه

### خطأ "bot was blocked"
- ألغِ حظر البوت في التلغرام
- أرسل `/start` مرة أخرى

## 📱 مثال على رسالة الدعم

```
👤 معلومات الزبون:
📝 الاسم: أحمد محمد
📞 الهاتف الأساسي: 07901234567
📱 الهاتف البديل: 07801234567

📍 معلومات العنوان:
🏛️ المحافظة: بغداد
🏠 العنوان: حي الكرادة

📦 معلومات الطلب:
🆔 رقم الطلب: order_123456
📅 تاريخ الطلب: 29/07/2025
⚠️ حالة الطلب: لا يرد
🚚 رقم الطلب في التوصيل: WAS_789

💬 ملاحظات المستخدم:
العميل لا يرد على الهاتف
```

## 🎯 النتيجة المتوقعة
بعد تطبيق هذه الخطوات:
- ✅ ستصل رسائل الدعم إلى التلغرام
- ✅ ستظهر رسالة نجاح في التطبيق
- ✅ سيتم تحديث حالة الطلب في قاعدة البيانات
